# This should be run after 'start-services'.
name: Tests that require bt services
inputs:
  run_serial:
    type: boolean
    description: "Run tests in serial mode rather than parallel"
    required: false
    default: false
  skip_tests:
    type: boolean
    description: "Skip running tests"
    required: false
    default: false
  DOCKERHUB_USERNAME:
    type: string
    required: true
  DOCKERHUB_TOKEN:
    type: string
    required: true
  ORB_API_KEY:
    type: string
    required: true
  DD_API_KEY:
    type: string
    required: true
runs:
  using: "composite"
  steps:
    - name: Install Braintrust dependencies
      uses: ./.github/actions/deps

    - uses: ./.github/actions/start-services
      with:
        DOCKERHUB_USERNAME: ${{ inputs.DOCKERHUB_USERNAME }}
        DOCKERHUB_TOKEN: ${{ inputs.DOCKERHUB_TOKEN }}
        ORB_API_KEY: ${{ inputs.ORB_API_KEY }}

    - name: Check SQL migrations are up to date.
      shell: bash
      run: |
        eval "$(mise activate)"
        generate-migration app > /tmp/migration.sql
        if [[ $(wc -c < /tmp/migration.sql) != "0" ]]; then
          echo "app schema.sql is inconsistent with migrations";
          bash -c "exit 1";
        fi
        generate-migration api > /tmp/migration.sql
        if [[ $(wc -c < /tmp/migration.sql) != "0" ]]; then
          echo "api schema.sql is inconsistent with migrations";
          bash -c "exit 1";
        fi

    - name: Run extra migrations
      shell: bash
      run: |
        eval "$(mise activate)"
        python api-schema/lambda_function.py --execute --run-all-in-foreground

    - name: Run bt_services tests
      shell: bash
      env:
        # DD_CIVISIBILITY_AGENTLESS_ENABLED: true
        # DD_API_KEY: ${{ inputs.DD_API_KEY }}
        # DD_SITE: us5.datadoghq.com
        # DD_ENV: ci
        # Force color output in Github Actions: https://github.com/pytest-dev/pytest/pull/7466
        FORCE_COLOR: 1
      run: |
        eval "$(mise activate)"
        TEST_ARGS=" --verbose ${{ inputs.run_serial == 'true' && '--serial' || '' }} -- tests/bt_services " make test

    - name: Check for internal server errors in test logs
      shell: bash
      run: |
        eval "$(mise activate)"
        if rg 'INTERNAL SERVER ERROR' $(./services/bt_services.py logfile api-ts); then
          echo "Found internal server errors in api-ts logs";
          bash -c "exit 1";
        fi
        if rg 'INTERNAL SERVER ERROR' $(./services/bt_services.py logfile test-proxy); then
          echo "Found internal server errors in test-proxy logs";
          bash -c "exit 1";
        fi

    - name: Generate upload file archive
      if: always()
      shell: bash
      run: find services/data/bt-logs -type f -print0 | xargs -0 tar --ignore-failed-read -czf logfiles.tar.gz
      continue-on-error: true

    - uses: actions/upload-artifact@v4
      if: always()
      with:
        name: logfiles-test-bt-services
        path: logfiles.tar.gz
        compression-level: 0
