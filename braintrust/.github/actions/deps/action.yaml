name: Deps
description: Install dependencies for the project
inputs:
  skip_cache:
    description: "Skip caching steps if true"
    required: false
    default: "false"
runs:
  using: "composite"
  steps:
    # Compute cache hashes on a clean repo, and just once. Doing it in the cache
    # step might redo work.
    - name: Compute cache hashes
      shell: bash
      run: |
        echo "nodeModulesCacheHash=${{ hashFiles('**/package-lock.json', '**/pnpm-lock.yaml') }}" >> "$GITHUB_ENV"
        echo "pyCacheHash=${{ hashFiles('**/*requirements*.txt') }}" >> "$GITHUB_ENV"

    - uses: jdx/mise-action@v2

    - name: Save tool versions
      shell: bash
      run: |
        echo "nodeVersion=$(node --version)" >> "$GITHUB_ENV"
        echo "pythonVersion=$(python --version | awk '{print $2}')" >> "$GITHUB_ENV"

    - name: Compute cache paths
      shell: bash
      run: |
        PNPM_CACHE_PATH=$(pnpm store path)
        UV_CACHE_PATH=$(uv cache dir)
        echo "pnpmCachePath=$PNPM_CACHE_PATH" >> "$GITHUB_ENV"
        echo "uvCachePath=$UV_CACHE_PATH" >> "$GITHUB_ENV"

    - name: Cache pnpm
      if: ${{ inputs.skip_cache != 'true' }}
      uses: WarpBuilds/cache@v1
      with:
        path: ${{ env.pnpmCachePath }}
        key: ${{ runner.os }}-${{ runner.arch }}-${{ env.nodeVersion }}-pnpm-store-${{ env.nodeModulesCacheHash }}
        restore-keys: |
          ${{ runner.os }}-${{ runner.arch }}-${{ env.nodeVersion }}-pnpm-store-

    - name: Cache uv
      if: ${{ inputs.skip_cache != 'true' }}
      uses: WarpBuilds/cache@v1
      with:
        path: ${{ env.uvCachePath }}
        key: ${{ runner.os }}-${{ runner.arch }}-${{ env.pythonVersion }}-uv-cache-${{ env.pyCacheHash }}
        restore-keys: |
          ${{ runner.os }}-${{ runner.arch }}-${{ env.pythonVersion }}-uv-cache-

    - name: Install deps
      shell: bash
      run: make develop

    - name: Enable turborepo caching
      # Note that the env vars need to be defined for the entire workflow run,
      # so we cannot just pass them as inputs into this action.
      if: ${{ env.TURBO_TEAM && env.TURBO_TOKEN && !inputs.skip_cache }}
      shell: bash
      run: npx turbo link --yes --scope braintrustdata

    - name: Build CI packages
      shell: bash
      run: |
        eval "$(mise activate)"
        ./scripts/pnpm_build_packages_with_binaries.py
        pnpm install
        ./scripts/pnpm_build_root_workspace_packages.py
