# This should be run after 'deps'.
name: Tests that do not require bt services
runs:
  using: "composite"
  steps:
    - name: Check openapi schema is up to date.
      shell: bash
      run: |
        eval "$(mise activate)"
        make openapi-spec-diff
    - name: Check BTQL AST is up to date.
      shell: bash
      run: |
        eval "$(mise activate)"
        make btql-ast-diff
    - name: Check Python SDK types are up to date.
      shell: bash
      run: |
        eval "$(mise activate)"
        make python-sdk-types-diff
    # NOTE: Temporarily disabled until we push brainstore live
    # - name: Check docker compose files are up to date.
    #   shell: bash
    #   run: |
    #     eval "$(mise activate)"
    #     # diff deployment/docker/docker-compose.full.yml <(generate-docker-compose full) > /tmp/docker_compose_diff.txt
    #     # if [[ $(wc -c < /tmp/docker_compose_diff.txt) != "0" ]]; then
    #     #   echo "docker-compose full is inconsistent with autogenerated file";
    #     #   bash -c "exit 1";
    #     # fi
    #     diff deployment/docker/docker-compose.api.yml <(generate-docker-compose api) > /tmp/docker_compose_diff.txt
    #     if [[ $(wc -c < /tmp/docker_compose_diff.txt) != "0" ]]; then
    #       echo "docker-compose api is inconsistent with autogenerated file";
    #       bash -c "exit 1";
    #     fi
    - name: Check the Brainstore schema is up to date.
      shell: bash
      run: |
        cp brainstore/examples/braintrust/schema.json /tmp/brainstore_schema.json
        make brainstore/examples/braintrust/schema.json
        diff /tmp/brainstore_schema.json brainstore/examples/braintrust/schema.json > /tmp/brainstore_schema_diff.txt
        if [[ $(wc -c < /tmp/brainstore_schema_diff.txt) != "0" ]]; then
          echo "Brainstore schema is inconsistent with autogenerated file";
          bash -c "exit 1";
        fi
    - name: Check that all TS test files compile
      shell: bash
      run: |
        eval "$(mise activate)"
        npx tsup --dts-only $(find tests -name '*.ts' | grep -v nocompile)
    - name: Check that all packages build, except the main webapp (and fumadocs since the build is unreliable).
      shell: bash
      run: |
        eval "$(mise activate)"
        pnpm build --filter "!braintrustdata" --filter "!*fumadocs*"
    - name: Type check Python code with Pyright
      shell: bash
      # TODO: Make Pyright errors fail the build.
      run: |
        pnpm dlx pyright@1.1.388 --level error $(git ls-files '*.py') || true
    - name: Run no_bt_services tests
      shell: bash
      run: |
        eval "$(mise activate)"
        python -m bt_unittest --no-bt-services ${{ inputs.run_serial && '--serial -- tests/no_bt_services' || 'tests/no_bt_services' }}
    - name: Check that all links in app/content are valid
      if: github.ref == 'refs/heads/main'
      shell: bash
      run: |
        eval "$(mise activate)"
        python scripts/check_links.py
