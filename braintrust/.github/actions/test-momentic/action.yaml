# This should be run after 'start-services'.
name: Momentic tests that require bt services
inputs:
  DOCKERHUB_USERNAME:
    type: string
    required: true
  DOCKERHUB_TOKEN:
    type: string
    required: true
  ORB_API_KEY:
    type: string
    required: true
  MOMENTIC_API_KEY:
    type: string
    required: true
  OPENAI_API_KEY:
    type: string
    required: true
runs:
  using: "composite"
  steps:
    - uses: ./.github/actions/deps

    - uses: ./.github/actions/start-services
      with:
        DOCKERHUB_USERNAME: ${{ inputs.DOCKERHUB_USERNAME }}
        DOCKERHUB_TOKEN: ${{ inputs.DOCKERHUB_TOKEN }}
        ORB_API_KEY: ${{ inputs.ORB_API_KEY }}

    - name: Run extra migrations
      shell: bash
      run: |
        eval "$(mise activate)"
        python api-schema/lambda_function.py --execute --run-all-in-foreground

    - name: Cache Momentic browsers
      id: cache-momentic
      uses: actions/cache@v4
      with:
        path: ~/.cache/momentic
        key: ${{ runner.os }}-momentic-browsers

    - name: Install Momentic browsers
      if: steps.cache-momentic.outputs.cache-hit != 'true'
      run: npx momentic@latest install-browsers
      shell: bash

    - run: |
        eval "$(mise activate)"
        ./scripts/run_sql_files.py app/supabase/seed.sql
        cd app
        export BRAINTRUST_API_KEY=$(../scripts/generate_api_key.py)
        pnpm test-momentic
      shell: bash
      env:
        MOMENTIC_API_KEY: ${{ inputs.MOMENTIC_API_KEY }}
        OPENAI_API_KEY: ${{ inputs.OPENAI_API_KEY }}
