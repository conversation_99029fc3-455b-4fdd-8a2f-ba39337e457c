name: Propose Release

on:
  schedule:
    - cron: "0 16 * * 1" # every Monday 8:00 PST / 9:00 PDT
  workflow_dispatch:
    inputs:
      release_version:
        description: "Release version. If not provided, the next patch version will be used."
        required: false

permissions:
  contents: write
  pull-requests: write
  actions: write

jobs:
  create-release-pr:
    if: ${{ github.event_name == 'workflow_dispatch' || github.event_name == 'schedule' }}
    runs-on: ubuntu-latest
    steps:
      - uses: actions/create-github-app-token@v1
        id: bot-token
        with:
          app-id: ${{ secrets.GH_BOT_APP_ID }}
          private-key: ${{ secrets.GH_BOT_APP_PRIVATE_KEY }}

      - uses: actions/checkout@v3
        with:
          ref: "main"
          fetch-depth: 0

      - name: Get last semantic tag
        id: last_tag
        run: |
          LAST_TAG=$(git tag --list "v[0-9]*.[0-9]*.[0-9]*" --sort=-v:refname | head -n 1)
          echo "Found last tag: $LAST_TAG"
          if [ -z "$LAST_TAG" ]; then
            LAST_TAG="v0.0.0"
          fi
          echo "LAST_TAG=$LAST_TAG" >> "$GITHUB_ENV"
          echo "last_tag=$LAST_TAG" >> "$GITHUB_OUTPUT"

      - name: Compute new version
        id: new_version
        run: |
          if [ -n "${{ github.event.inputs.release_version }}" ]; then
            # Use manually provided version
            MANUAL_VERSION="${{ github.event.inputs.release_version }}"
            echo "Using manually provided version: $MANUAL_VERSION"
            echo "NEW_VERSION=$MANUAL_VERSION" >> "$GITHUB_ENV"
            echo "new_version=$MANUAL_VERSION" >> "$GITHUB_OUTPUT"
          else
            # Compute next patch version
            OLD_TAG=${{ steps.last_tag.outputs.last_tag }}
            OLD_VERSION=${OLD_TAG#v}
            IFS='.' read -r MAJOR MINOR PATCH <<< "$OLD_VERSION"
            NEW_PATCH=$((PATCH + 1))
            NEW_VERSION="v${MAJOR}.${MINOR}.${NEW_PATCH}"
            echo "Computed next version: $NEW_VERSION"
            echo "NEW_VERSION=$NEW_VERSION" >> "$GITHUB_ENV"
            echo "new_version=$NEW_VERSION" >> "$GITHUB_OUTPUT"
          fi

      - name: Create release branch and update VERSION file
        id: create_branch
        run: |
          set -e  # Exit on any error
          NEW_BRANCH="release/${{ steps.new_version.outputs.new_version }}"

          # Check if branch already exists
          if git ls-remote --heads origin "$NEW_BRANCH" | grep -q "$NEW_BRANCH"; then
            echo "::error::Branch $NEW_BRANCH already exists. There is likely a pending release PR."
            exit 1
          fi

          git checkout -b "$NEW_BRANCH"
          echo "${{ steps.new_version.outputs.new_version }}" > VERSION
          git config user.name "github-actions[bot]"
          git config user.email "github-actions[bot]@users.noreply.github.com"
          git add VERSION
          git commit -m "Propose release ${{ steps.new_version.outputs.new_version }}"
          git push -f origin "$NEW_BRANCH"
          echo "BRANCH_NAME=$NEW_BRANCH" >> "$GITHUB_ENV"

      - name: Generate release notes
        id: release_notes
        uses: actions/github-script@v6
        with:
          script: |
            const lastTag = process.env.LAST_TAG;
            const newVersion = process.env.NEW_VERSION;
            const diffUrl = `https://github.com/${context.repo.owner}/${context.repo.repo}/compare/${lastTag}...main`;

            let releaseNotes = `**Proposed release ${newVersion}**\n\n`;
            releaseNotes += `Merging this PR will create a new release and publish all artifacts as \`${newVersion}\` and \`latest\`. You can modify the VERSION file to change the version number if desired.\n\n`;
            releaseNotes += "## What's Changed\n";
            releaseNotes += `Diff: ${diffUrl}\n\n`;

            // Get commits since last tag
            const compareRef = lastTag === 'v0.0.0' ?
              await github.rest.repos.listCommits({
                owner: context.repo.owner,
                repo: context.repo.repo,
                per_page: 100
              }) :
              await github.rest.repos.compareCommits({
                owner: context.repo.owner,
                repo: context.repo.repo,
                base: lastTag,
                head: 'HEAD'
              });

            const commits = lastTag === 'v0.0.0' ?
              compareRef.data :
              compareRef.data.commits;

            if (commits.length === 0) {
              releaseNotes += '- No changes since last release\n';
            } else {
              for (const commit of commits) {
                const message = commit.commit.message.split('\n')[0];
                const author = commit.commit.author.name;
                const authorLogin = commit.author ? commit.author.login : author;

                // Extract PR number if present
                const prMatch = message.match(/\(#(\d+)\)/);
                const prNumber = prMatch ? prMatch[1] : null;

                // Remove PR number from message
                const cleanMessage = message.replace(/\(#\d+\)/, '').trim();

                // Format the entry
                if (prNumber) {
                  const prUrl = `https://github.com/${context.repo.owner}/${context.repo.repo}/pull/${prNumber}`;
                  releaseNotes += `* ${cleanMessage} (${authorLogin} in [#${prNumber}](${prUrl}))\n`;
                } else {
                  releaseNotes += `* ${cleanMessage} (${authorLogin})\n`;
                }
              }
            }

            await core.summary
              .addRaw(releaseNotes)
              .write();
            core.notice('Generated release notes');
            core.setOutput('notes', releaseNotes);
            core.exportVariable('RELEASE_NOTES', releaseNotes);
            return releaseNotes;

      - name: Create Pull Request for release
        id: create_pr
        uses: actions/github-script@v6
        with:
          github-token: ${{ steps.bot-token.outputs.token }}
          script: |
            const branch = process.env.BRANCH_NAME;
            const newVersion = process.env.NEW_VERSION;
            const prTitle = `Release ${newVersion}`;
            const prBody = process.env.RELEASE_NOTES;

            let prNumber;
            try {
              // Check if PR already exists
              const existingPRs = await github.rest.pulls.list({
                owner: context.repo.owner,
                repo: context.repo.repo,
                head: `${context.repo.owner}:${branch}`,
                base: 'main'
              });

              if (existingPRs.data.length > 0) {
                // Update existing PR
                const existingPR = existingPRs.data[0];
                prNumber = existingPR.number;
                await github.rest.pulls.update({
                  owner: context.repo.owner,
                  repo: context.repo.repo,
                  pull_number: prNumber,
                  title: prTitle,
                  body: prBody
                });
                core.notice(`Updated existing PR #${prNumber}`);
              } else {
                // Create new PR
                const response = await github.rest.pulls.create({
                  owner: context.repo.owner,
                  repo: context.repo.repo,
                  head: branch,
                  base: 'main',
                  title: prTitle,
                  body: prBody
                });
                prNumber = response.data.number;
                core.notice(`Created new PR #${prNumber}`);
              }
              core.setOutput("pr_number", prNumber);
            } catch (error) {
              core.setFailed(`Failed to create/update PR: ${error.message}`);
              throw error;
            }
            return prNumber;

      - name: Notify Slack
        if: success()
        uses: slackapi/slack-github-action@v2.1.0
        with:
          webhook: ${{ secrets.SLACK_WEBHOOK_URL }}
          webhook-type: incoming-webhook
          payload: |
            text: "Release PR ${{ steps.create_pr.outputs.pr_number }} created for ${{ steps.new_version.outputs.new_version }}"
            blocks:
              - type: "section"
                text:
                  type: "mrkdwn"
                  text: "${{ github.event_name == 'schedule' && '📅 Weekly Release PR Created' || '🚀 Release PR Created' }} for ${{ steps.new_version.outputs.new_version }}\n\nReview the PR and merge it to create a new release.\n\nPR: https://github.com/${{ github.repository }}/pull/${{ steps.create_pr.outputs.pr_number }}"
