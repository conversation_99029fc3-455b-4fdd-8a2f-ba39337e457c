# When merging PRs which contain supabase migrations, folks should make sure to
# follow this playbook:
# https://www.notion.so/Playbook-for-merging-Supabase-Migrations-dffc10b425a84ca5bb664e9bb85bf41c.
#
# This pre-merge check enforces the first step of the playbook: that
# auto-promotion is disabled on vercel if the PR contains new supabase
# migrations.
name: Supabase Check no Vercel Auto-Deploy
env:
  VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
  VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID }}
  VERCEL_TOKEN: ${{ secrets.VERCEL_TOKEN }}
  FETCH_DEPTH: 10000
on:
  workflow_dispatch:
    inputs:
      debug_enabled:
        type: boolean
        description: "Run the build with tmate debugging enabled"
        required: false
        default: false
  pull_request:
jobs:
  supabase-vercel-no-autodeploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: ${FETCH_DEPTH}
      - name: Fetch main branch
        shell: bash
        run: |
          git fetch origin main --depth ${FETCH_DEPTH}
      - name: Run check
        env:
          VERCEL_SKIP_DOMAINS: ${{ vars.VERCEL_SKIP_DOMAINS }}
        shell: bash
        run: |
          git diff $(git merge-base HEAD origin/main) -- app/supabase/migrations/ | wc -c > supabase_migrations_wc.txt
          if [[ $(cat supabase_migrations_wc.txt) != "0" ]] && [[ "$VERCEL_SKIP_DOMAINS" == "false" ]]; then
            echo 'Detected supabase migrations in this PR. Before merging this PR, make sure to set the VERCEL_SKIP_DOMAINS github variable to true, as detailed in https://www.notion.so/Playbook-for-merging-Supabase-Migrations-dffc10b425a84ca5bb664e9bb85bf41c. Reach out to @manu for help';
            bash -c "exit 1";
          fi
      - uses: ./.github/actions/terminal
        if: ${{ (success() || failure()) && github.event_name == 'workflow_dispatch' && inputs.debug_enabled }}
