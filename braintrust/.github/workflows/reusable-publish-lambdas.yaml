name: Publish Lambdas
on:
  workflow_call:
    inputs:
      release_as_latest:
        type: boolean
        description: "Release live to users as 'latest'"
        required: false
        default: false
      publish_release_tag:
        type: string
        description: "Publish with custom release tag. Optional"
        required: false
      publish_git_sha:
        type: string
        description: "Publish tagged with git SHA. Optional"
        required: false
      debug_enabled:
        type: boolean
        description: "Run the build with tmate debugging enabled"
        required: false
        default: false

jobs:
  publish-lambdas:
    if: ${{ inputs.release_as_latest == true || inputs.publish_release_tag != '' || inputs.publish_git_sha != '' }}
    runs-on: warp-ubuntu-2404-x64-8x
    permissions:
      id-token: write # OIDC permissions for AWS auth
      contents: read
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 1

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::872608195481:role/github_ecr_full_access
          aws-region: us-east-1

      - name: Install Braintrust dependencies
        uses: ./.github/actions/deps

      - name: Publish Lambdas to S3 as SHA
        if: ${{ inputs.publish_git_sha }}
        run: |
          eval "$(mise activate)"
          make publish-lambdas ARGS="${{ inputs.publish_git_sha }}"

      - name: Publish Lambdas to S3 as latest
        if: ${{ inputs.release_as_latest }}
        run: |
          eval "$(mise activate)"
          # Note this actually builds and zips the lambdas too
          make publish-lambdas ARGS="latest"

      - name: Publish Lambdas to S3 with custom release tag
        if: ${{ inputs.publish_release_tag }}
        run: |
          eval "$(mise activate)"
          make publish-lambdas ARGS="${{ inputs.publish_release_tag }}"

      - uses: ./.github/actions/terminal
        if: ${{ (success() || failure()) && inputs.debug_enabled }}
