import argparse
import os
import subprocess

import duckdb

conn = duckdb.connect(database=":memory:")

SCRIPT_DIR = os.path.dirname(os.path.realpath(__file__))


def combine_files():
    subprocess.check_call(f"mkdir -p {SCRIPT_DIR}/scratch")
    subprocess.check_call(f"find ${SCRIPT_DIR}/data/ -name '*.gz' -exec cat {{}} \; > scratch/combined.gz")


def setup_views(reset_data=False):
    if reset_data:
        conn.query(
            """
        COPY (
            SELECT * REPLACE(timestamp::timestamptz AS timestamp, originalTimestamp::timestamptz AS originalTimestamp)
            FROM read_ndjson_auto('scratch/combined.gz', sample_size=-1, union_by_name=true)
        )
        TO 'data/segment_prod.parquet'
        """
        )
    conn.query(
        """
        CREATE VIEW segment_prod AS
            SELECT * FROM 'data/segment_prod.parquet'
    """
    )


queries = {
    "DAU": """
        SELECT
            DATE_TRUNC('day', timestamp), COUNT(DISTINCT userId)
        FROM segment_prod
        WHERE userId IS NOT NULL AND properties.category IS NOT NULL GROUP BY ALL ORDER BY 1
""",
    "DAU_raw": """
        SELECT
            DATE_TRUNC('day', timestamp), userId
        FROM segment_prod
        WHERE userId IS NOT NULL AND properties.category IS NOT NULL GROUP BY ALL ORDER BY 1
""",
    "WAU": """
        SELECT
            DATE_TRUNC('week', timestamp), COUNT(DISTINCT userId)
        FROM segment_prod
        WHERE userId IS NOT NULL AND properties.category IS NOT NULL GROUP BY ALL ORDER BY 1
""",
    "WAU_raw": """
        SELECT
            DATE_TRUNC('week', timestamp), userId
        FROM segment_prod
        WHERE userId IS NOT NULL AND properties.category IS NOT NULL GROUP BY ALL ORDER BY 1
""",
    "MAU": """
        SELECT
            DATE_TRUNC('month', timestamp), COUNT(DISTINCT userId)
        FROM segment_prod
        WHERE userId IS NOT NULL AND properties.category IS NOT NULL GROUP BY ALL ORDER BY 1
""",
    "MAU_raw": """
        SELECT
            DATE_TRUNC('month', timestamp), userId
        FROM segment_prod
        WHERE userId IS NOT NULL AND properties.category IS NOT NULL GROUP BY ALL ORDER BY 1
""",
}

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--reset-data", action="store_true")
    args = parser.parse_args()

    #    if args.reset_data:
    #        subprocess.check_call(["./sync_data.sh"])
    #        combine_files()

    setup_views(reset_data=args.reset_data)

    results_dir = os.path.join(SCRIPT_DIR, "results")
    os.makedirs(results_dir, exist_ok=True)

    for name, query in queries.items():
        result_file = os.path.join(results_dir, name + ".csv")
        conn.query(
            f"""
        COPY (
            {query}
        ) TO '{result_file}'
        """
        )
