{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "type": "lldb",
      "request": "launch",
      "name": "Debug lldb",
      "program": "${workspaceFolder}/<executable file>",
      "args": ["--nocapture"],
      "cwd": "${workspaceFolder}"
    },
    {
      "name": "Next.js: debug webapp",
      "type": "node-terminal",
      "request": "launch",
      "command": "pnpm dev",
      "cwd": "${workspaceFolder}/app",
      "internalConsoleOptions": "neverOpen"
    },
    {
      "name": "api-ts",
      "type": "node",
      "request": "launch",
      "runtimeExecutable": "${workspaceFolder}/api-ts/node_modules/nodemon/bin/nodemon.js",
      "program": "${workspaceFolder}/api-ts/dist/local.js",
      "args": ["--inspect", "--enable-source-maps"],
      "runtimeArgs": [
        "--watch", "${workspaceFolder}/api-ts/dist/local.js"
    ],
      "restart": true,
      "console": "integratedTerminal",
      "internalConsoleOptions": "neverOpen"
  },
    {
      "type": "node",
      "request": "attach",
      "name": "Attach to Node",
      "port": 9230,
      "cwd": "${workspaceFolder}/app"
    },
    {
      "type": "node",
      "request": "attach",
      "name": "Attach to api-ts",
      "port": 9240,
      "cwd": "${workspaceFolder}/api-ts",
      "autoAttachChildProcesses": true,
      "restart": true,
      "skipFiles": ["<node_internals>/**", "**/node_modules/**"],
      "remoteRoot": "${workspaceFolder}/api-ts",
      "localRoot": "${workspaceFolder}/api-ts",
      "sourceMaps": true,
      "resolveSourceMapLocations": [
        "${workspaceFolder}/**",
        "!**/node_modules/**"
      ]
    },
    {
      "type": "node",
      "request": "attach",
      "name": "Attach to test-proxy",
      "port": 9250,
      "cwd": "${workspaceFolder}/api-ts",
      "autoAttachChildProcesses": true,
      "restart": true,
      "skipFiles": ["<node_internals>/**", "**/node_modules/**"],
      "remoteRoot": "${workspaceFolder}/api-ts",
      "localRoot": "${workspaceFolder}/api-ts",
      "sourceMaps": true,
      "resolveSourceMapLocations": [
        "${workspaceFolder}/**",
        "!**/node_modules/**"
      ]
    },
    {
      "type": "node",
      "request": "attach",
      "name": "Attach to webapp",
      "port": 9261, // the nextjs debug port is +1 from whatever we NODE_OPTIONS='--inspect=9260' with
      "skipFiles": ["<node_internals>/**", "**/node_modules/**"]
    }
  ],
  "compounds": [
    {
      "name": "Attach to all services",
      "configurations": [
        "Attach to api-ts",
        "Attach to test-proxy",
        "Attach to webapp"
      ]
    }
  ]
}
